{"timestamp": "2025-06-20T12:04:25.6711479Z", "outcome": "purchase_failed", "reason": "Purchase attempt failed: Required quantity already reached", "itemData": {"itemId": "326649091657", "title": "B38 Allen-Bradley Overload Heater Element", "currentPrice": 9.97, "condition": "New", "seller": "witmermotorservice", "shippingCost": 0.0, "location": "US, Pennsylvania, Stevens, 175**", "endTime": "2025-07-19T19:05:04", "buyItNowPrice": 9.97, "auctionPrice": null, "quantityAvailable": 3, "listingType": "Buy it Now, BEST_OFFER", "itemUrl": "https://www.ebay.com/itm/326649091657/", "imageUrl": "https://i.ebayimg.com/00/s/OTY4WDk2OA==/z/5qQAAOSwR7ZoVF8p/$_57.JPG?set_id=880000500F", "categoryId": "181706", "categoryName": "Business & Industrial|Industrial Automation & Motion Controls|Drives & Starters|Starters|Other Starters", "conditionDescription": null, "description": "                                     ", "feedbackScore": 5842.0, "feedbackRating": 100.0, "bids": 0, "bestOffer": true, "autoPay": true, "site": "US"}, "keywordState": {"KeywordId": "1cb-4530-a305-e00a223aeb76", "Alias": "326649091657", "JobId": "7", "Keywords": "326649091657", "SearchInDescription": false, "PriceMin": 0.1, "PriceMax": 10000.0, "Categories": "", "Condition": [], "EbaySiteName": "eBay US", "LocatedIn": "Any", "AvailableTo": "US", "Zip": "27603", "Sellers": [""], "SellerType": "", "Frequency": "00:00:00", "Threads": 1, "ViewName": "Results", "ListingType": ["BuyItNow"], "RequiredQuantity": 3, "PurchasedQuantity": 3, "CapturedAt": "2025-06-20T14:04:25.6711479+02:00", "RemainingQuantity": 0, "CompletionPercentage": 100.0, "IsCompleted": true, "ConditionString": "", "SellersString": "", "ListingTypeString": "BuyItNow"}, "filterRule": {"filterAlias": "restock", "expression": "Contains([Title], 'allen')", "matched": true, "evaluationResult": "Filter matched - item processed", "evaluatedAt": "2025-06-20T12:04:25.6711479Z"}, "transactionResult": {"attempted": true, "success": false, "transactionId": null, "errorMessage": "Required quantity already reached", "purchasePrice": null, "quantity": null, "completedAt": null}}