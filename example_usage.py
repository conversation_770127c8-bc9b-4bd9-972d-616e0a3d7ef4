#!/usr/bin/env python3
"""
Example usage of the Layout Bricks Instructions Analyzer
"""

import os
from pathlib import Path
from analyze_layout_bricks import Layout<PERSON>ricks<PERSON><PERSON>y<PERSON>

def create_sample_files():
    """Create some sample .txt files for testing."""
    sample_folder = Path("sample_files")
    sample_folder.mkdir(exist_ok=True)
    
    # Sample file 1: Contains layout bricks instructions
    with open(sample_folder / "layout_instructions.txt", "w") as f:
        f.write("""
Layout Bricks Assembly Instructions

Step 1: Place the foundation bricks in a 4x4 grid pattern
Step 2: Stack the wall bricks on top, ensuring proper alignment
Step 3: Insert the corner connector bricks at each intersection
Step 4: Add the top layer bricks to complete the structure
Step 5: Verify all bricks are securely connected

Note: Each brick should click into place with the adjacent bricks.
Make sure the layout follows the provided blueprint pattern.
""")
    
    # Sample file 2: Regular text file
    with open(sample_folder / "regular_text.txt", "w") as f:
        f.write("""
This is just a regular text file about cooking recipes.
Today we will learn how to make pasta with tomato sauce.
First, boil water in a large pot.
Add salt and pasta to the boiling water.
Cook for 8-10 minutes until al dente.
Drain and serve with your favorite sauce.
""")
    
    # Sample file 3: UI layout instructions
    with open(sample_folder / "ui_layout.txt", "w") as f:
        f.write("""
UI Component Layout Guide

Arrange the interface bricks as follows:
- Header brick: Position at top, full width
- Navigation brick: Below header, left-aligned
- Content bricks: Main area, arranged in 2x3 grid
- Sidebar brick: Right side, fixed width
- Footer brick: Bottom, full width

Each UI brick should snap to the grid system.
Use the brick connector components to link related elements.
""")
    
    print(f"Created sample files in: {sample_folder.absolute()}")
    return sample_folder

def main():
    """Example usage of the analyzer."""
    
    # Check if API key is set
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("Warning: OPENROUTER_API_KEY environment variable not set.")
        print("You can set it with: export OPENROUTER_API_KEY='your_key_here'")
        print("Or pass it directly to the analyzer.")
        return
    
    # Create sample files for testing
    sample_folder = create_sample_files()
    
    try:
        # Initialize analyzer
        print("Initializing Layout Bricks Analyzer...")
        analyzer = LayoutBricksAnalyzer()
        
        # Run analysis
        print(f"Analyzing files in: {sample_folder}")
        results = analyzer.analyze_folder(str(sample_folder))
        
        # Display results
        print("\n" + "="*60)
        print("ANALYSIS RESULTS")
        print("="*60)
        
        for result in results:
            print(f"\nFile: {result['file_path']}")
            print(f"Score: {result['score']}/10")
            print(f"Confidence: {result['confidence']}")
            print(f"Keywords: {', '.join(result['keywords_found'])}")
            print(f"Reasoning: {result['reasoning'][:100]}...")
            
        # Summary statistics
        scores = [r['score'] for r in results]
        print(f"\nSummary:")
        print(f"Total files: {len(results)}")
        print(f"Average score: {sum(scores)/len(scores):.2f}")
        print(f"High-scoring files (7+): {len([s for s in scores if s >= 7])}")
        
    except Exception as e:
        print(f"Error during analysis: {e}")

if __name__ == "__main__":
    main()
