# Layout Bricks Instructions Analyzer

This Python script analyzes all `.txt` files in a folder and its subfolders to detect "layout bricks instructions" using LiteLLM with OpenRouter's DeepSeek model.

## Features

- Recursively scans folders for `.txt` files
- Uses DeepSeek R1 model via OpenRouter for intelligent analysis
- Customizable analysis prompt loaded from `_prompt.txt` file
- Provides scores from 1-10 for likelihood of containing layout bricks instructions
- Outputs results to console and log file
- Creates detailed reasoning files for each analyzed file
- Generates JSON summary of all results

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your OpenRouter API key (choose one method):

   **Option A: Environment variable**
   ```bash
   export OPENROUTER_API_KEY="your_api_key_here"
   ```

   **Option B: .env file (recommended)**
   Create a `.env` file in the project directory:
   ```
   OPENROUTER_API_KEY=your_api_key_here
   ```

   **Option C: Command line parameter**
   Pass it directly when running the script.

## Usage

Basic usage:
```bash
python analyze_layout_bricks.py /path/to/your/txt/files
```

With custom output folder:
```bash
python analyze_layout_bricks.py /path/to/your/txt/files --output-folder /path/to/output
```

With API key parameter:
```bash
python analyze_layout_bricks.py /path/to/your/txt/files --api-key your_api_key_here
```

With custom prompt file:
```bash
python analyze_layout_bricks.py /path/to/your/txt/files --prompt-file my_custom_prompt.txt
```

## Customizing the Analysis Prompt

The script reads the analysis prompt from `_prompt.txt` by default. You can:

1. **Edit the default prompt**: Modify `_prompt.txt` to change how the AI analyzes files
2. **Use a custom prompt file**: Create your own prompt file and specify it with `--prompt-file`
3. **Prompt format**: Use `{file_content}` as a placeholder where the file content will be inserted

The prompt should instruct the AI to return JSON with:
- `score`: Integer from 1-10
- `reasoning`: Detailed explanation
- `keywords_found`: List of relevant keywords
- `confidence`: "high", "medium", or "low"

## Output

The script creates:

1. **Console output**: Real-time progress and results
2. **Log file**: `layout_bricks_analysis.log` with detailed logging
3. **Output folder** containing:
   - Individual reasoning files for each analyzed file
   - `analysis_summary.json` with all results

## Score Interpretation

- **1-2**: Definitely not layout bricks instructions
- **3-4**: Unlikely to be layout bricks instructions
- **5-6**: Possibly contains some layout/construction elements
- **7-8**: Likely contains layout bricks instructions
- **9-10**: Definitely contains layout bricks instructions

## Example Output

```
File: /path/to/file1.txt | Score: 8/10
File: /path/to/file2.txt | Score: 3/10
File: /path/to/file3.txt | Score: 9/10

Statistics:
Total files analyzed: 3
Average score: 6.67
Files with high scores (7+): 2
```

## Requirements

- Python 3.7+
- OpenRouter API key
- Internet connection for API calls
