#!/usr/bin/env python3
"""
Test script to verify .env file loading
"""

import os

# Try to load .env file if python-dotenv is available
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Successfully loaded .env file")
except ImportError:
    print("❌ python-dotenv not installed")

# Check if API key is loaded
api_key = os.getenv('OPENROUTER_API_KEY')
if api_key:
    print(f"✅ API key loaded: {api_key[:10]}...{api_key[-10:]}")
else:
    print("❌ API key not found in environment variables")

# Test the analyzer initialization
try:
    from analyze_layout_bricks import LayoutBricksAnalyzer
    analyzer = LayoutBricksAnalyzer()
    print("✅ LayoutBricksAnalyzer initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize analyzer: {e}")
