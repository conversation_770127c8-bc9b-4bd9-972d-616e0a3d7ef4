#!/usr/bin/env python3
"""
Layout Bricks Instructions Analyzer

This script analyzes all .txt files in a folder and its subfolders to detect
"layout bricks instructions" using LiteLLM with OpenRouter's DeepSeek model.

Requirements:
- litellm
- openrouter API key

Usage:
    python analyze_layout_bricks.py <input_folder> [output_folder]
"""

import os
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import litellm

# Try to load .env file if python-dotenv is available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # python-dotenv not installed, continue without it

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('layout_bricks_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LayoutBricksAnalyzer:
    def __init__(self, api_key: str = None, prompt_file: str = "_prompt.txt"):
        """Initialize the analyzer with OpenRouter API key and prompt file."""
        self.api_key = api_key or os.getenv('OPENROUTER_API_KEY')
        if not self.api_key:
            raise ValueError("OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable or pass it directly.")

        # Configure LiteLLM for OpenRouter
        litellm.api_key = self.api_key
        litellm.api_base = "https://openrouter.ai/api/v1"

        self.model = "deepseek/deepseek-r1-0528:free"
        self.prompt_file = prompt_file
        self.prompt_template = self.load_prompt_template()

    def load_prompt_template(self) -> str:
        """Load the prompt template from file."""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                template = f.read()
            logger.info(f"Loaded prompt template from: {self.prompt_file}")
            return template
        except FileNotFoundError:
            logger.error(f"Prompt file not found: {self.prompt_file}")
            raise FileNotFoundError(f"Prompt template file '{self.prompt_file}' not found. Please create this file with your analysis prompt.")
        except Exception as e:
            logger.error(f"Error loading prompt template: {e}")
            raise

    def create_analysis_prompt(self, file_content: str) -> str:
        """Create the prompt for analyzing file content using the loaded template."""
        return self.prompt_template.format(file_content=file_content)

    def parse_ai_response(self, response_text: str) -> Dict:
        """Parse AI response and extract JSON data with multiple fallback strategies."""
        import re

        # Strategy 1: Look for JSON block between ```json and ```
        json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_text, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group(1))
            except json.JSONDecodeError:
                pass

        # Strategy 2: Look for JSON block between ``` and ```
        json_match = re.search(r'```\s*(\{.*?\})\s*```', response_text, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group(1))
            except json.JSONDecodeError:
                pass

        # Strategy 3: Find the first complete JSON object
        start_idx = response_text.find('{')
        if start_idx != -1:
            brace_count = 0
            end_idx = start_idx

            for i, char in enumerate(response_text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break

            if brace_count == 0:
                try:
                    json_str = response_text[start_idx:end_idx]
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    pass

        # Strategy 4: Try to extract individual fields using regex
        score_match = re.search(r'"score":\s*(\d+)', response_text)
        reasoning_match = re.search(r'"reasoning":\s*"([^"]*)"', response_text)
        keywords_match = re.search(r'"keywords_found":\s*\[(.*?)\]', response_text)
        confidence_match = re.search(r'"confidence":\s*"([^"]*)"', response_text)

        if score_match:
            score = int(score_match.group(1))
            reasoning = reasoning_match.group(1) if reasoning_match else "Could not extract reasoning"
            keywords = []
            if keywords_match:
                keywords_str = keywords_match.group(1)
                keywords = [k.strip().strip('"') for k in keywords_str.split(',') if k.strip()]
            confidence = confidence_match.group(1) if confidence_match else "low"

            return {
                "score": score,
                "reasoning": reasoning,
                "keywords_found": keywords,
                "confidence": confidence
            }

        # Strategy 5: Last resort - return error with full response
        raise ValueError(f"Could not parse JSON from response: {response_text[:500]}...")

    def analyze_file(self, file_path: Path) -> Dict:
        """Analyze a single file for layout bricks instructions."""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": "File is empty",
                    "keywords_found": [],
                    "confidence": "high",
                    "error": None
                }

            # Create prompt
            prompt = self.create_analysis_prompt(content)

            # Call LiteLLM with OpenRouter
            response = litellm.completion(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )

            # Parse response
            response_text = response.choices[0].message.content
            logger.debug(f"Raw response for {file_path}: {response_text}")

            # Try to extract JSON from response with improved parsing
            try:
                analysis_result = self.parse_ai_response(response_text)

            except Exception as e:
                logger.warning(f"Failed to parse AI response for {file_path}: {e}")
                # Fallback: create basic result
                analysis_result = {
                    "score": 1,
                    "reasoning": f"Failed to parse AI response: {str(e)}. Raw response: {response_text[:200]}...",
                    "keywords_found": [],
                    "confidence": "low"
                }

            # Add file path and ensure score is valid
            analysis_result["file_path"] = str(file_path)
            analysis_result["score"] = max(1, min(10, int(analysis_result.get("score", 1))))
            analysis_result["error"] = None

            return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return {
                "file_path": str(file_path),
                "score": 1,
                "reasoning": f"Error during analysis: {str(e)}",
                "keywords_found": [],
                "confidence": "low",
                "error": str(e)
            }

    def find_txt_files(self, input_folder: Path) -> List[Path]:
        """Find all .txt files in the folder and subfolders."""
        txt_files = []
        for root, dirs, files in os.walk(input_folder):
            for file in files:
                if file.lower().endswith('.txt'):
                    txt_files.append(Path(root) / file)
        return txt_files

    def save_reasoning_file(self, analysis_result: Dict, output_folder: Path):
        """Save detailed reasoning to a separate file."""
        file_path = Path(analysis_result["file_path"])

        # Create a safe filename based on the original file path
        safe_filename = str(file_path).replace('\\', '_').replace('/', '_').replace(':', '_')
        reasoning_filename = f"{safe_filename}_analysis.txt"
        reasoning_path = output_folder / reasoning_filename

        # Ensure output directory exists
        reasoning_path.parent.mkdir(parents=True, exist_ok=True)

        # Write reasoning file
        with open(reasoning_path, 'w', encoding='utf-8') as f:
            f.write(f"Layout Bricks Instructions Analysis\n")
            f.write(f"{'=' * 50}\n\n")
            f.write(f"Original File: {analysis_result['file_path']}\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Score: {analysis_result['score']}/10\n")
            f.write(f"Confidence: {analysis_result['confidence']}\n\n")
            f.write(f"Keywords Found: {', '.join(analysis_result['keywords_found'])}\n\n")
            f.write(f"Detailed Reasoning:\n")
            f.write(f"{'-' * 20}\n")
            f.write(f"{analysis_result['reasoning']}\n")

            if analysis_result.get('error'):
                f.write(f"\nError: {analysis_result['error']}\n")

    def analyze_folder(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Analyze all txt files in the specified folder."""
        input_path = Path(input_folder)

        if not input_path.exists():
            raise FileNotFoundError(f"Input folder does not exist: {input_folder}")

        # Set up output folder
        if output_folder:
            output_path = Path(output_folder)
        else:
            output_path = input_path / "layout_bricks_analysis_output"

        output_path.mkdir(parents=True, exist_ok=True)

        # Find all txt files
        txt_files = self.find_txt_files(input_path)
        logger.info(f"Found {len(txt_files)} .txt files to analyze")

        if not txt_files:
            logger.warning("No .txt files found in the specified folder")
            return []

        results = []

        # Analyze each file
        for i, file_path in enumerate(txt_files, 1):
            logger.info(f"Analyzing file {i}/{len(txt_files)}: {file_path}")

            try:
                result = self.analyze_file(file_path)
                results.append(result)

                # Save reasoning file
                self.save_reasoning_file(result, output_path)

                # Log result
                logger.info(f"File: {result['file_path']} | Score: {result['score']}/10")

            except Exception as e:
                logger.error(f"Failed to analyze {file_path}: {e}")
                results.append({
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"Analysis failed: {str(e)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(e)
                })

        # Save summary results
        summary_path = output_path / "analysis_summary.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        logger.info(f"Analysis complete. Results saved to: {output_path}")
        return results

def main():
    parser = argparse.ArgumentParser(description="Analyze text files for layout bricks instructions")
    parser.add_argument("input_folder", help="Path to the folder containing .txt files to analyze")
    parser.add_argument("--output-folder", help="Path to the output folder (optional)")
    parser.add_argument("--api-key", help="OpenRouter API key (optional, can use OPENROUTER_API_KEY env var)")
    parser.add_argument("--prompt-file", default="_prompt.txt", help="Path to the prompt template file (default: _prompt.txt)")

    args = parser.parse_args()

    try:
        # Initialize analyzer
        analyzer = LayoutBricksAnalyzer(api_key=args.api_key, prompt_file=args.prompt_file)

        # Run analysis
        results = analyzer.analyze_folder(args.input_folder, args.output_folder)

        # Print summary
        print(f"\n{'='*60}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*60}")

        for result in results:
            print(f"File: {result['file_path']}")
            print(f"Score: {result['score']}/10")
            print(f"Confidence: {result['confidence']}")
            if result.get('error'):
                print(f"Error: {result['error']}")
            print("-" * 40)

        # Statistics
        scores = [r['score'] for r in results if not r.get('error')]
        if scores:
            avg_score = sum(scores) / len(scores)
            high_scores = len([s for s in scores if s >= 7])
            print(f"\nStatistics:")
            print(f"Total files analyzed: {len(results)}")
            print(f"Average score: {avg_score:.2f}")
            print(f"Files with high scores (7+): {high_scores}")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
