#!/usr/bin/env python3
"""
Layout Bricks Instructions Analyzer

This script analyzes all .txt files in a folder and its subfolders to detect
"layout bricks instructions" using LiteLLM with OpenRouter's DeepSeek model.

Requirements:
- litellm
- openrouter API key

Usage:
    python analyze_layout_bricks.py <input_folder> [output_folder]
"""

import os
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
import litellm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('layout_bricks_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LayoutBricksAnalyzer:
    def __init__(self, api_key: str = None):
        """Initialize the analyzer with OpenRouter API key."""
        self.api_key = api_key or os.getenv('OPENROUTER_API_KEY')
        if not self.api_key:
            raise ValueError("OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable or pass it directly.")
        
        # Configure LiteLLM for OpenRouter
        litellm.api_key = self.api_key
        litellm.api_base = "https://openrouter.ai/api/v1"
        
        self.model = "deepseek/deepseek-r1-0528:free"
        
    def create_analysis_prompt(self, file_content: str) -> str:
        """Create the prompt for analyzing file content."""
        return f"""
Analyze the following text content to determine if it contains "layout bricks instructions" or similar content related to building layouts with brick-like components, UI layout instructions, or construction/assembly instructions using modular brick elements.

Text content to analyze:
{file_content}

Please provide your analysis in the following JSON format:
{{
    "score": <integer from 1 to 10>,
    "reasoning": "<detailed explanation of your analysis and why you gave this score>",
    "keywords_found": ["<list of relevant keywords or phrases found>"],
    "confidence": "<high/medium/low>"
}}

Score guidelines:
- 1-2: Definitely not layout bricks instructions
- 3-4: Unlikely to be layout bricks instructions
- 5-6: Possibly contains some layout/construction elements
- 7-8: Likely contains layout bricks instructions
- 9-10: Definitely contains layout bricks instructions

Focus on identifying:
- Instructions for arranging or placing brick-like elements
- Layout patterns or configurations
- Assembly instructions for modular components
- UI/interface layout instructions using brick metaphors
- Construction guides using brick-based systems
"""

    def analyze_file(self, file_path: Path) -> Dict:
        """Analyze a single file for layout bricks instructions."""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if not content.strip():
                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": "File is empty",
                    "keywords_found": [],
                    "confidence": "high",
                    "error": None
                }
            
            # Create prompt
            prompt = self.create_analysis_prompt(content)
            
            # Call LiteLLM with OpenRouter
            response = litellm.completion(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            
            # Parse response
            response_text = response.choices[0].message.content
            
            # Try to extract JSON from response
            try:
                # Find JSON in the response
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}') + 1
                
                if start_idx != -1 and end_idx != 0:
                    json_str = response_text[start_idx:end_idx]
                    analysis_result = json.loads(json_str)
                else:
                    raise ValueError("No JSON found in response")
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Failed to parse JSON response for {file_path}: {e}")
                # Fallback: try to extract score manually
                score = 1
                reasoning = response_text
                keywords_found = []
                confidence = "low"
                
                analysis_result = {
                    "score": score,
                    "reasoning": reasoning,
                    "keywords_found": keywords_found,
                    "confidence": confidence
                }
            
            # Add file path and ensure score is valid
            analysis_result["file_path"] = str(file_path)
            analysis_result["score"] = max(1, min(10, int(analysis_result.get("score", 1))))
            analysis_result["error"] = None
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return {
                "file_path": str(file_path),
                "score": 1,
                "reasoning": f"Error during analysis: {str(e)}",
                "keywords_found": [],
                "confidence": "low",
                "error": str(e)
            }

    def find_txt_files(self, input_folder: Path) -> List[Path]:
        """Find all .txt files in the folder and subfolders."""
        txt_files = []
        for root, dirs, files in os.walk(input_folder):
            for file in files:
                if file.lower().endswith('.txt'):
                    txt_files.append(Path(root) / file)
        return txt_files

    def save_reasoning_file(self, analysis_result: Dict, output_folder: Path):
        """Save detailed reasoning to a separate file."""
        file_path = Path(analysis_result["file_path"])
        
        # Create a safe filename based on the original file path
        safe_filename = str(file_path).replace('\\', '_').replace('/', '_').replace(':', '_')
        reasoning_filename = f"{safe_filename}_analysis.txt"
        reasoning_path = output_folder / reasoning_filename
        
        # Ensure output directory exists
        reasoning_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write reasoning file
        with open(reasoning_path, 'w', encoding='utf-8') as f:
            f.write(f"Layout Bricks Instructions Analysis\n")
            f.write(f"{'=' * 50}\n\n")
            f.write(f"Original File: {analysis_result['file_path']}\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Score: {analysis_result['score']}/10\n")
            f.write(f"Confidence: {analysis_result['confidence']}\n\n")
            f.write(f"Keywords Found: {', '.join(analysis_result['keywords_found'])}\n\n")
            f.write(f"Detailed Reasoning:\n")
            f.write(f"{'-' * 20}\n")
            f.write(f"{analysis_result['reasoning']}\n")
            
            if analysis_result.get('error'):
                f.write(f"\nError: {analysis_result['error']}\n")

    def analyze_folder(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Analyze all txt files in the specified folder."""
        input_path = Path(input_folder)
        
        if not input_path.exists():
            raise FileNotFoundError(f"Input folder does not exist: {input_folder}")
        
        # Set up output folder
        if output_folder:
            output_path = Path(output_folder)
        else:
            output_path = input_path / "layout_bricks_analysis_output"
        
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Find all txt files
        txt_files = self.find_txt_files(input_path)
        logger.info(f"Found {len(txt_files)} .txt files to analyze")
        
        if not txt_files:
            logger.warning("No .txt files found in the specified folder")
            return []
        
        results = []
        
        # Analyze each file
        for i, file_path in enumerate(txt_files, 1):
            logger.info(f"Analyzing file {i}/{len(txt_files)}: {file_path}")
            
            try:
                result = self.analyze_file(file_path)
                results.append(result)
                
                # Save reasoning file
                self.save_reasoning_file(result, output_path)
                
                # Log result
                logger.info(f"File: {result['file_path']} | Score: {result['score']}/10")
                
            except Exception as e:
                logger.error(f"Failed to analyze {file_path}: {e}")
                results.append({
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"Analysis failed: {str(e)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(e)
                })
        
        # Save summary results
        summary_path = output_path / "analysis_summary.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Analysis complete. Results saved to: {output_path}")
        return results

def main():
    parser = argparse.ArgumentParser(description="Analyze text files for layout bricks instructions")
    parser.add_argument("input_folder", help="Path to the folder containing .txt files to analyze")
    parser.add_argument("--output-folder", help="Path to the output folder (optional)")
    parser.add_argument("--api-key", help="OpenRouter API key (optional, can use OPENROUTER_API_KEY env var)")
    
    args = parser.parse_args()
    
    try:
        # Initialize analyzer
        analyzer = LayoutBricksAnalyzer(api_key=args.api_key)
        
        # Run analysis
        results = analyzer.analyze_folder(args.input_folder, args.output_folder)
        
        # Print summary
        print(f"\n{'='*60}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*60}")
        
        for result in results:
            print(f"File: {result['file_path']}")
            print(f"Score: {result['score']}/10")
            print(f"Confidence: {result['confidence']}")
            if result.get('error'):
                print(f"Error: {result['error']}")
            print("-" * 40)
        
        # Statistics
        scores = [r['score'] for r in results if not r.get('error')]
        if scores:
            avg_score = sum(scores) / len(scores)
            high_scores = len([s for s in scores if s >= 7])
            print(f"\nStatistics:")
            print(f"Total files analyzed: {len(results)}")
            print(f"Average score: {avg_score:.2f}")
            print(f"Files with high scores (7+): {high_scores}")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
